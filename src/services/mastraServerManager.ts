import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class MastraServerManager {
    private mastraProcess: ChildProcess | null = null;
    private context: vscode.ExtensionContext;
    private port: number = 4111;
    private isProduction: boolean;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;

        // 从配置中获取设置
        const config = vscode.workspace.getConfiguration('aicode');
        this.port = config.get('mastraPort', 4111);

        // 判断是否为生产环境（打包后的插件或强制生产模式）
        const forceProductionMode = config.get('useProductionMode', false);
        this.isProduction = context.extensionMode === vscode.ExtensionMode.Production || forceProductionMode;
    }

    async start(): Promise<void> {
        try {
            console.log('当前开发环境',this.isProduction);
            if (this.isProduction) {
                await this.startProductionServer();
            } else {
                await this.startDevelopmentServer();
            }

            // 等待服务器启动
            await this.waitForServer();
            vscode.window.showInformationMessage(`Mastra 服务器已启动在端口 ${this.port}`);
        } catch (error) {
            vscode.window.showErrorMessage(`启动 Mastra 服务器失败: ${error}`);
        }
    }

    private async startDevelopmentServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();

        // 检查是否有 pnpm
        const packageManager = await this.detectPackageManager(workspaceRoot);
        console.log('Package manager:', packageManager);
        // 使用检测到的包管理器启动开发服务器
        this.mastraProcess = spawn(packageManager, ['npm','run', 'dev', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            }
        });

        this.setupProcessHandlers('开发');
    }

    private async startProductionServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();

        // 检测包管理器
        const packageManager = await this.detectPackageManager(workspaceRoot);

        // 首先构建项目
        await this.buildProject(packageManager);

        // 然后启动生产服务器
        this.mastraProcess = spawn(packageManager, ['run', 'start', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'production'
            }
        });

        this.setupProcessHandlers('生产');
    }

    private async buildProject(packageManager: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const workspaceRoot = this.getWorkspaceRoot();

            const buildProcess = spawn(packageManager, ['run', 'build'], {
                cwd: workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            buildProcess.stdout?.on('data', (data) => {
                // Build output
            });

            buildProcess.stderr?.on('data', (data) => {
                // Build error output
            });

            buildProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`构建失败，退出码: ${code}`));
                }
            });

            buildProcess.on('error', (error) => {
                reject(error);
            });
        });
    }

    private async detectPackageManager(workspaceRoot: string): Promise<string> {
        const fs = require('fs');

        // 检查 pnpm-lock.yaml
        if (fs.existsSync(path.join(workspaceRoot, 'pnpm-lock.yaml'))) {
            return 'pnpm';
        }

        // 检查 yarn.lock
        if (fs.existsSync(path.join(workspaceRoot, 'yarn.lock'))) {
            return 'yarn';
        }

        // 检查 package-lock.json
        if (fs.existsSync(path.join(workspaceRoot, 'package-lock.json'))) {
            return 'npm';
        }

        // 默认使用 npm
        return 'npm';
    }

    private setupProcessHandlers(mode: string): void {
        if (!this.mastraProcess) return;

        this.mastraProcess.stdout?.on('data', (data) => {
            // Process output
        });

        this.mastraProcess.stderr?.on('data', (data) => {
            // Process error output
        });

        this.mastraProcess.on('close', (code) => {
            this.mastraProcess = null;
        });

        this.mastraProcess.on('error', (error) => {
            vscode.window.showErrorMessage(`Mastra ${mode}服务器错误: ${error.message}`);
        });
    }

    private async waitForServer(): Promise<void> {
        const maxAttempts = 30;
        const delay = 10000;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 健康检查：尝试连接服务器
                const response = await fetch(`http://localhost:${this.port}`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(3000)
                });

                if (response.ok) {
                    return;
                }
            } catch (error) {
                // 服务器还未准备好，继续等待
            }
        console.log('等待Mastra服务器启动...');

            await new Promise(resolve => setTimeout(resolve, delay));
        }

        throw new Error('Mastra 服务器启动超时，请检查日志');
    }

    private getWorkspaceRoot(): string {

        // 获取插件所在的工作区根目录
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        // 如果没有工作区，使用插件目录
        return this.context.extensionPath;
    }

    stop(): void {
        if (this.mastraProcess) {
            this.mastraProcess.kill('SIGTERM');
            this.mastraProcess = null;
        }
    }

    getServerUrl(): string {
        return `http://localhost:${this.port}`;
    }

    getApiUrl(): string {
        return `${this.getServerUrl()}/api`;
    }

    isRunning(): boolean {
        return this.mastraProcess !== null && !this.mastraProcess.killed;
    }
}
